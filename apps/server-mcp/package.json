{"name": "@gauzy/server-mcp", "version": "0.1.0", "description": "Ever Gauzy MCP Server - A Model Context Protocol server as an Electron desktop application", "type": "module", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "license": "AGPL-3.0", "private": true, "main": "index.js", "scripts": {"build": "yarn nx build server-mcp --configuration=development", "build:prod": "yarn nx build server-mcp --configuration=production", "start": "electron .", "start:mcp": "node build/mcp-server.js"}, "dependencies": {"@angular/animations": "^19.2.10", "@angular/cdk": "^19.2.11", "@angular/common": "^19.2.10", "@angular/core": "^19.2.10", "@angular/forms": "^19.2.10", "@angular/material": "^19.2.11", "@angular/router": "^19.2.10", "@electron/remote": "^2.0.8", "@gauzy/config": "^0.1.0", "@modelcontextprotocol/sdk": "^1.13.1", "@nebular/auth": "^15.0.0", "@nebular/bootstrap": "^9.1.0-rc.6", "@nebular/eva-icons": "^15.0.0", "@nebular/security": "^15.0.0", "@nebular/theme": "^15.0.0", "@ng-select/ng-select": "^14.8.1", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@sentry/angular-ivy": "^7.101.1", "@sentry/electron": "^4.18.0", "custom-electron-titlebar": "^4.2.8", "dotenv": "^16.4.5", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "rxjs": "^7.8.2", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@electron/rebuild": "^3.2.10", "@types/node": "^22.14.0", "electron": "^30.0.1", "electron-builder": "^24.13.3", "rimraf": "^3.0.2", "typescript": "^5.8.3"}}