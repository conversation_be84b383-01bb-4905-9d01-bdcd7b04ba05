import { NgModule, provideAppInitializer } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule, HttpClient } from '@angular/common/http';
import { RouterModule } from '@angular/router';

// Nebular modules
import {
	NbThemeModule,
	NbLayoutModule,
	NbSidebarModule,
	NbMenuModule,
	NbCardModule,
	NbButtonModule,
	NbIconModule,
	NbDialogModule,
	NbToastrModule,
	NbSpinnerModule,
	NbActionsModule,
	NbContextMenuModule,
	NbPopoverModule,
	NbSearchModule,
	NbSelectModule,
	NbInputModule,
	NbToggleModule,
	NbProgressBarModule,
	NbListModule,
	NbTabsetModule,
	NbAccordionModule,
	NbUserModule,
	NbCheckboxModule,
	NbRadioModule,
	NbDatepickerModule,
	NbChatModule
} from '@nebular/theme';
import { NbEvaIconsModule } from '@nebular/eva-icons';

// NGX-translate
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

// Sentry
import * as Sentry from '@sentry/angular-ivy';
import { Router } from '@angular/router';
import { APP_INITIALIZER } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { AppRoutingModule } from './app-routing.module.js';
import { AppComponent } from './app.component.js';
import { DashboardComponent } from './dashboard/dashboard.component.js';
import { McpServerComponent } from './mcp-server/mcp-server.component.js';
import { SettingsComponent } from './settings/settings.component.js';

// Translation loader function
export function HttpLoaderFactory(http: HttpClient) {
	return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
	declarations: [AppComponent, DashboardComponent, McpServerComponent, SettingsComponent],
	imports: [
		BrowserModule,
		BrowserAnimationsModule,
		HttpClientModule,
		RouterModule,
		FormsModule,
		AppRoutingModule,

		// Nebular Theme
		NbThemeModule.forRoot({ name: 'default' }),
		NbLayoutModule,
		NbSidebarModule.forRoot(),
		NbMenuModule.forRoot(),
		NbCardModule,
		NbButtonModule,
		NbIconModule,
		NbDialogModule.forRoot(),
		NbToastrModule.forRoot(),
		NbSpinnerModule,
		NbActionsModule,
		NbContextMenuModule,
		NbPopoverModule,
		NbSearchModule,
		NbSelectModule,
		NbInputModule,
		NbToggleModule,
		NbProgressBarModule,
		NbListModule,
		NbTabsetModule,
		NbAccordionModule,
		NbUserModule,
		NbCheckboxModule,
		NbRadioModule,
		NbDatepickerModule.forRoot(),
		NbChatModule,
		NbEvaIconsModule,

		// NGX-translate
		TranslateModule.forRoot({
			loader: {
				provide: TranslateLoader,
				useFactory: HttpLoaderFactory,
				deps: [HttpClient]
			}
		})
	],
	providers: [
		{
			provide: Sentry.TraceService,
			deps: [Router]
		},
		provideAppInitializer(() => {
            const initializerFn = ((trace: Sentry.TraceService) => () => { })(inject(Sentry.TraceService));
            return initializerFn();
        }),
	],
	bootstrap: [AppComponent]
})
export class AppModule {}
